//
//  VideoTranscriptionService.swift
//  ExportVideo
//
//  Created by <PERSON> on 21/06/2021.
//

import Foundation
import Speech
import SwiftUI

enum VideoTranscriptionServiceStatus: Equatable {
    case idle
    case extractAudio
    case inProgress(Int)
    case noCaptionDetected
    case error
}

enum VideoTranscriptionServiceSource {
    case vl // we use transription for the video library
    case clip // we use transription for clips captionning

    var analyticsHeader: String {
        switch self {
        case .clip:
            return "Clip Caption"
        case .vl:
            return "Live Recording"
        }
    }
}

@MainActor
class VideoTranscriptionService: ObservableObject {

    private var onSuccess: (([Caption]) -> Void)?

    @Published var auth: SFSpeechRecognizerAuthorizationStatus = .notDetermined
    @Published var status: VideoTranscriptionServiceStatus = .idle
    @Published var recognizedCaptions: [Caption] = [Caption]()
    @Published var errorNoAuthorization = false
    private var tempCaptions: [Caption] = [Caption]()
    private var currentTask: SFSpeechRecognitionTask?

    var factory = VideoTranscriptionCaptionFactory()
    var assetUrl: URL?

    private var isCancelled = false
    private var source: VideoTranscriptionServiceSource

    var languagesProvider: TranscriptionLanguagesProvider

    init(source: VideoTranscriptionServiceSource, assetUrl: URL?) {
        self.source = source
        self.auth = SFSpeechRecognizer.authorizationStatus()
        self.assetUrl = assetUrl
        self.languagesProvider = TranscriptionLanguagesProvider()
        self.languagesProvider.source = source
    }

    func requestAuthorization() {
        SFSpeechRecognizer.requestAuthorization { [weak self] authStatus in
            DispatchQueue.main.async {
                self?.auth = authStatus
            }
        }
    }

    public func getCaption(seconds: Double) -> Caption? {
        return recognizedCaptions.filter { caption in
            return caption.trim.start <= seconds && caption.trim.end >= seconds
        }.first
    }

    public func cancel() {
        currentTask?.cancel()
        currentTask = nil
        isCancelled = true
        self.status = .idle
    }

    public func start(onSuccess: @escaping (([Caption]) -> Void)) {

        self.onSuccess = onSuccess

        Analytics.shared.reportEvent(event: TranscriptEventType.startAutocaptioning(
            source: source,
            language: languagesProvider.currentLanguage))
        isCancelled = false
        currentTask?.cancel()

        guard let assetUrl = assetUrl else {
            self.status = .error
            return
        }

        withAnimation {
            self.status = .extractAudio
        }

        resolveURL(assetUrl: assetUrl)
    }

    private func resolveURL(assetUrl: URL) {
        UrlResolver.default.fullyResolve(assetUrl) { (urlHolder: UrlHolder) in

            if self.isCancelled { return }

            if let url = urlHolder.resolvedUrl {
                let asset = AVAsset(url: url)
                self.startRequestAuthorization(asset: asset)
            }
        }
    }

    private func startRequestAuthorization(asset: AVAsset) {
        self.errorNoAuthorization = false
        SFSpeechRecognizer.requestAuthorization { [unowned self] authStatus in
            DispatchQueue.main.async {

                if self.isCancelled {
                    return
                }

                if authStatus == .notDetermined {
                    // just return
                    return
                } else if authStatus == .authorized {
                    let directory = NSTemporaryDirectory()
                    let fileName = NSUUID().uuidString
                    // This returns a URL? even though it is an NSURL class method
                    let fullURL = NSURL.fileURL(withPathComponents: [directory, fileName])

                    guard let audioURL = fullURL?.appendingPathExtension("m4a") else {
                        self.manageError()
                        return
                    }

                    asset.writeAudioTrack(to: audioURL) {
                        self.transcribeAudio(url: audioURL)
                    } failure: { (_) in
                        self.manageError()
                    }

                } else {
                    self.manageAnalytics(status: "Error Authorization")
                    self.status = .idle
                    self.errorNoAuthorization = true
                }
            }
        }
    }

    private func manageAnalytics(status: String) {
        Analytics.shared.reportEvent(event: TranscriptEventType.endAutocaptioning(
            source: source,
            language: languagesProvider.currentLanguage,
            status: status,
            captions: recognizedCaptions.count))
    }

    private func manageError() {
        self.manageAnalytics(status: "Error")
        self.status = .error
        self.currentTask = nil
    }

    private func manageSuccess() {
        self.manageAnalytics(status: "Success")
        self.status = .idle
        self.currentTask = nil
        onSuccess?(recognizedCaptions)
    }

    private func transcribeAudio(url: URL) {

        guard !self.isCancelled else { return }

        currentTask?.cancel()

        DispatchQueue.main.async {
            self.tempCaptions = []
            self.status = .inProgress(0)
        }

        // create a new recognizer and point it at our audio
        guard languagesProvider.currentLanguage != "",
              let recognizer = SFSpeechRecognizer(locale: Locale(identifier: languagesProvider.currentLanguage)),
              recognizer.isAvailable,
              recognizer.supportsOnDeviceRecognition,
              FileManager.default.fileExists(atPath: url.path) else {
            self.manageError()
            return
        }

        let request = SFSpeechURLRecognitionRequest(url: url)
        request.requiresOnDeviceRecognition = true
        request.shouldReportPartialResults = false
        request.addsPunctuation = true

        // start recognition
        currentTask = recognizer.recognitionTask(with: request, resultHandler: resultHandler)
    }

    private func resultHandler(result: SFSpeechRecognitionResult?, error: (any Error)?) {
        if currentTask?.isCancelled ?? false || self.isCancelled {
            return
        }

        // abort if we didn't get any transcription back
        guard let result = result else {

            // we can have an error but send previous caption
            if tempCaptions.count > 0 {
                print("There was an error: \(error!)")
                recognizedCaptions = tempCaptions
                self.manageSuccess()
                return
            } else {
                // manage error here
                self.manageAnalytics(status: "No Caption")
                self.status = .noCaptionDetected
                print("There was an error: \(error!)")
                return
            }
        }

        Toaster.sendToastMessage(message: result.bestTranscription.formattedString,
                                 style: .dark,
                                 showOnTop: true)

        // if we got the final transcription back, print i
        let segments = result.bestTranscription.segments
        let captionsTemp = factory.createCaptions(from: segments)
        tempCaptions.append(contentsOf: captionsTemp)
        self.status = .inProgress(tempCaptions.count)
        if result.isFinal && !(currentTask?.isCancelled ?? true) {
            recognizedCaptions = tempCaptions
            self.manageSuccess()
            return
        }
    }
}

private extension AVAsset {

    private func audioAsset() async throws -> AVAsset {
        // Create a new container to hold the audio track
        let composition = AVMutableComposition()
        // Create an array of audio tracks in the given asset
        // Typically, there is only one
        let audioTracks = try await loadTracks(withMediaType: .audio)

        // Iterate through the audio tracks while
        // Adding them to a new AVAsset
        for track in audioTracks {
            let compositionTrack = composition.addMutableTrack(withMediaType: .audio,
                                                               preferredTrackID: kCMPersistentTrackID_Invalid)
            let timeRange = try await track.load(.timeRange)
            try compositionTrack?.insertTimeRange(timeRange,
                                                  of: track,
                                                  at: timeRange.start)
        }

        return composition
    }

    // Provide a URL for where you wish to write
    // the audio file if successful
    func writeAudioTrack(to url: URL,
                         success: @escaping () -> Void,
                         failure: @escaping (any Error) -> Void) {
        Task {
            do {
                let asset = try await audioAsset()
                await asset.write(to: url, success: success, failure: failure)
            } catch {
                failure(error)
            }
        }
    }

    private func write(to url: URL,
                       success: @escaping () -> Void,
                       failure: @escaping (any Error) -> Void) async {
        // Create an export session that will output an
        // audio track (M4A file)

        try? FileManager.default.removeItem(at: url)

        guard let exportSession = AVAssetExportSession(asset: self,
                                                       presetName: AVAssetExportPresetAppleM4A) else {
                                                        // This is just a generic error
                                                        let error = NSError(domain: "",
                                                                            code: -1,
                                                                            userInfo: nil)
                                                        failure(error)

                                                        return
        }

        exportSession.outputFileType = .m4a
        exportSession.outputURL = url

        await exportSession.export()

        switch exportSession.status {
        case .completed:
            success()
        default:
            let error = exportSession.error ?? NSError(domain: "", code: -1, userInfo: nil)
            failure(error)
        }
    }
}
