//
//  SelectSourceViewModel.swift
//  Cap-iOS
//
//  Created by <PERSON> on 20/10/2021.
//  Copyright © 2021 Switcher Inc. All rights reserved.
//

import Foundation
import SwiftUI
import Combine

@MainActor
protocol ElementCollectionViewControllerDelegate: AnyObject {
    func elementCollectionDidSelectElement(_ element: Element)
    func elementCollectionDidDeselectElement(_ element: Element)
}

/*
 * This is the core model to manage the Element
 */
@MainActor
class SelectSourceViewModel: NSObject, ObservableObject {

    let selectedBorderWidth = 4.0

    @Published var reminderElements: [Element] = [] // list of elements
    @Published var ratio: Double // ratio of elements
    @Published var sourceOrientation: Axis
        .Set = .vertical // if collection need to scroll horizontally, when iPhone landscape

    // used for retro compatibility with UIKit, in iPhone we need space in bottom
    @Published var contentInset: EdgeInsets
    @ObservedObject var dimensionModel: SelectSourceDimensionModel // Used when user update cell size
    @ObservedObject var sectionModel: SectionModel

    @Published var dragging: Element? // we use this when we start dragging
    @Published var isDragging: Bool = false // will be true when we start dropping

    // when user clicks on { "Select" to select severals cells
    @Published var selectedMode: Bool = false {
        didSet {
            if !selectedMode {
                // reset all selected values
                reminderElements.forEach {
                    $0.isSelected = false
                }
                sectionModel.deselectAll()
            }
        }
    }

    var cancellables = [AnyCancellable]()
    var mainMixer: MainMixer
    var actionManager: ElementCollectionActionManager?
    var controlModel: any ElementControlModel
    var pickerMode: Bool
    var showVideos: Bool
    lazy var logger = LsLogger(subsystem: "swi.ui", category: "ViewModel SelectSource")
    var displayChannelNumbers = false
    weak var parentView: UIView?
    let elementGroups: ElementGroupManager
    var placeholderElement: Element?
    var highlightedUrl: String?

    public weak var delegate: (any ElementCollectionViewControllerDelegate)?

    init(mainMixer: MainMixer,
         ratio: GmuRatioI32,
         controlModel: any ElementControlModel,
         pickerMode: Bool,
         contentInset: EdgeInsets = EdgeInsets(),
         showVideos: Bool = true,
         highlightedUrl: String? = nil) {
        self.controlModel = controlModel
        self.mainMixer = mainMixer
        self.ratio = ratio.doubleValue
        self.pickerMode = pickerMode
        self.contentInset = contentInset
        self.showVideos = showVideos
        self.displayChannelNumbers = !pickerMode
        self.elementGroups = ElementGroupManager.shared()
        self.dimensionModel = SelectSourceDimensionModel(ratio: ratio, forceColumnCount: pickerMode ? 3 : nil)
        self.sectionModel = SectionModel(elementGroup: elementGroups)
        self.highlightedUrl = highlightedUrl
        super.init()

        mainMixer.pilot.delegate = self

        sectionModel.addSection(Element.defaultSection)
        for group in self.elementGroups.groupList {
            sectionModel.addSection(group.name, displayStatus: group.displayStatus)
        }
        mainMixer.delegates.addChannelAvailabilityDelegate(self)
        mainMixer.delegates.addChannelEmploymentDelegate(self)

        self.elementGroups.addDelegate(self)
        dimensionModel.aspectRatio = ratio

        for channel in mainMixer.channelManager.channels {
            channelDidAppear(channel)
        }
        let c = dimensionModel.objectWillChange.sink(receiveValue: { [weak self] in
                                            self?.objectWillChange.send() })
        let d = sectionModel.objectWillChange.sink(receiveValue: { [weak self] in
                                            self?.objectWillChange.send() })
        self.cancellables.append(c)
        self.cancellables.append(d)
    }

    convenience init(mainMixer: MainMixer,
                     ratio: GmuRatioI32,
                     controlModel: any ElementControlModel,
                     pickerMode: Bool,
                     contentInset: EdgeInsets = EdgeInsets(),
                     inputManager: MultiviewInputManager,
                     index: Int) {
        let preselectedInput = inputManager.getPreselectedInput(index: index)
        let showVideos = preselectedInput?.type == .video ? true : inputManager.videosCount < 2
        self.init(mainMixer: mainMixer,
                  ratio: ratio,
                  controlModel: controlModel,
                  pickerMode: pickerMode,
                  contentInset: contentInset,
                  showVideos: showVideos,
                  highlightedUrl: preselectedInput?.url.absoluteString)

        NotificationCenter.default.addObserver(self,
                                               selector: #selector(toggleElement),
                                               name: .toggleElement,
                                               object: nil)
    }

    func add(element: Element) {

        if !sectionModel.hasSection(element.section) {
            FatalLogger.shared.logFatalError("Unknown section '\(element.section)' should never happened")
        }

        element.thumbnailMaxSize = CGSize(width: 240, height: 240 / ratio)
        self.reminderElements.insert(element, at: self.insertIndex(element: element))
        updateElementChannelLabel(element: element)
        element.loadThumbnailImage()
        _ = updateElementStatus(element: element)
        let c = element.objectWillChange.sink(receiveValue: { [weak self] in
                                            self?.objectWillChange.send() })
        self.cancellables.append(c)
    }

    func insertIndex(element: Element) -> Int {
        if element.channel.camera != nil {
            // it's a live source - shoud have an channel number - put in front
            assert(element.channel.index >= 0)
            let elementChannel = max(0, Int(element.channel.index))
            for i in 0 ..< reminderElements.count {
                let channel = reminderElements[i].channel.index
                if channel < 0 || channel > elementChannel {
                    return i
                }
            }
            return reminderElements.count
        } else {
            // it's not a live source - should have a rank
            assert(element.rank != nil)
            let elementRank = element.rank ?? MultilevelRank()
            for i in 0 ..< reminderElements.count {
                guard let rank = reminderElements[i].rank, reminderElements[i].channel.camera == nil else {
                    continue
                }
                if rank > elementRank {
                    return i
                }
            }
            return reminderElements.count
        }
    }

    func selectElement(element: Element, anchor: CGRect?) {

        // output element index
        logger.info("calling to StandardElementControlModel::elementTapAction::" + (reminderElements.firstIndex(of: element)?.description ?? "nil"))

        if selectedMode {

            if !element.canBeSelected {
                return
            }

            element.isSelected.toggle()
            if element.isSelected {
                delegate?.elementCollectionDidSelectElement(element)
            } else {
                delegate?.elementCollectionDidDeselectElement(element)
            }
            // If selected elements within a Section view change
            // the overall section is deselected
            sectionModel.deselect(element.section)
            return
        }

        controlModel.elementTapAction(element, sender: parentView, anchor: anchor)
    }

    // function to select an element at a given index
    func selectElement(at index: Int) {
        guard index >= 0 && index < reminderElements.count else {
            return
        }
        selectElement(element: reminderElements[index], anchor: nil)
    }

    @objc public func toggleElement(Notification: Notification) {
        if let index = Notification.userInfo?["index"] as? Int {
            selectElement(at: index)
        }
    }

    func selectAllElements(section: String, selected: Bool) {
        selected ? sectionModel.select(section) : sectionModel.deselect(section)
        for element in reminderElements.filter({$0.section == section && $0.canBeSelected }) {
            element.isSelected = selected
            if element.isSelected {
                delegate?.elementCollectionDidSelectElement(element)
            } else {
                delegate?.elementCollectionDidDeselectElement(element)
            }
        }
    }

    func delete(element: Element) {
        controlModel.remove(element)
    }

    func presentEditor(element: Element) {
        controlModel.presentEditor(element)
    }

    func duplicate(element: Element) {
        controlModel.duplicate(element)
    }

    private func isLiveSource(channelIndex: Int32) -> Bool {
        return channelIndex >= M2ChannelAllocator.VCH_FROM && channelIndex < M2ChannelAllocator.VCH_TO &&
             !(channelIndex >= M2ChannelAllocator.PCH_FROM && channelIndex < M2ChannelAllocator.PCH_TO)
    }

    /**
     * Channel labels show the camera numbers (1, 2, 3, ...) or the channel of
     * the dynamic multiview which they are involved in (A, B, C, ...).
     * They need to be update only when the dynamic multiview changes, i.e.
     * when:
     * - one channel involvel in the multiview is selected or unselected
     * - one channel involvel in the multiview disappears
     * - the dynamic multiview is changed or unselected
     * - we switch to pro mode (displaying the preview) where dynamic multiviews
     *   are just managed as static multiviews
     */
    private func updateCellChannelLabels(channels: Set<M2Channel>) {
        if !channels.isEmpty {
            for channel in channels {
                guard let element = reminderElements.filter({ $0.channel == channel}).first else {
                    continue
                }
                updateElementChannelLabel(element: element)
            }
        }
    }

    func updateElementChannelLabel(element: Element) {
        var text: String?
        var color: Color?
        let pilot = mainMixer.pilot
        let multiviewPhase = pilot.multiviewBuilder.phase
        let channel = element.channel
        let overlay = channel.isOverlay
        if !overlay {
            if multiviewPhase == .building {
                if let pos = pilot.previewManager.sources.firstIndex(where: { $0.channel == channel }) {
                    text = pos.toLetter()
                    color = Color.yellow
                }
            } else if multiviewPhase == .active {
                if let pos = pilot.programManager.sources.firstIndex(where: { $0.channel == channel }) {
                    text = pos.toLetter()
                    color = Color.yellow
                }
            }
        }

        if displayChannelNumbers && text == nil {
            let index = Int32(channel.index)
            if isLiveSource(channelIndex: index) {
                text = String(index + 1)
                color = Color.white
            }
        }

        element.channelLabelText = text
        if text != nil, let color = color {
            element.channelLabelColor = color
        }
    }

    // return true if the new status of the element need to refresh all elements
    func updateElementStatus(element: Element) -> Bool {
        let channel = element.channel
        if channel.isEffect && !channel.isScene {
            element.isAux = channel.isEmployed(in: .preview)
            return true
        } else {
            element.isInPreview = channel.isEmployed(in: .preview)
            element.isLive = channel.isLive
            updateElementChannelLabel(element: element)
        }
        return false
    }

    public var elemListSelected: [Element] {
        get {
            return reminderElements.filter {$0.isSelected}
        }
    }

}

extension SelectSourceViewModel: M2ChannelEmploymentDelegate {
    func channelEmploymentStateDidChange(_ channels: Set<M2Channel>) {
        var updateAllLabels = false
        for channel in channels {
            guard let element = reminderElements.filter({ $0.channel == channel}).first else {
                continue
            }
            let needUpdateAllElements = updateElementStatus(element: element)
            if needUpdateAllElements {
                updateAllLabels = true
            }
        }

        if updateAllLabels {
            // we update all channel labels
            if !mainMixer.pilot.isPreviewAvailable {
                updateCellChannelLabels(channels: Set<M2Channel>(mainMixer.pilot.programManager.sources
                    .map { $0.channel }))
            }
        }
    }
}

extension SelectSourceViewModel: M2ChannelAvailabilityDelegate {
    func channelDidAppear(_ channel: M2Channel) {
        if pickerMode {
            if channel.placeholderType == .checker {
                placeholderElement = Element(channel: channel)
                return
            }

            if channel.isOverlay ||
                channel.isAudioOnly ||
                channel.isScene ||
                channel.isPlayedSource && !showVideos {
                return
            }

            if channel.isEffect {
                let type = channel.effect!.type
                if type == .dynamicMultiview || type == .staticMultiview || type == .scene {
                    return
                }
            }
        }

        // Filter out hidden channels
        if channel.coreElement.isHidden {
            return
        }

        if channel.isEffect {
            let type = channel.effect!.type
            if type != .dynamicMultiview && type != .scene {
                return
            }
        }

        if let elem = Element(channel: channel) {
            add(element: elem)
        }
    }

    func channelWillDisappear(_ channel: M2Channel) {
        if let index = self.reminderElements.firstIndex(where: {$0.channel == channel}) {
            self.reminderElements.remove(at: index)

            /*
            * When the removed element is involved in a dynamic multiview which
            * is being built, the "A", "B", "C", ... labels are reassigned.
                * So, we need to refresh all those labels.
                */
            self
                .updateCellChannelLabels(channels: Set<M2Channel>(self.mainMixer.pilot.previewManager.sources
                .map { $0.channel }))
        }
    }
}

extension SelectSourceViewModel: ElementGroupsDelegate {
    func elementGroupsAddGroup(_ group: String, at: Int) {
        sectionModel.addSection(group, at: at + 1) // + 1 because there is the default group
    }

    func elementGroupsDidRemoveGroup(_ group: String, at: Int) {
        sectionModel.removeSection(group)
    }

    func elementGroupsMoveGroup(_ group: String, at: Int, to: Int) {
        sectionModel.moveSection(group, at: at + 1, to: to + 1)
    }

    func elementGroupsUpdateVisibility(_ group: String, newStatus: ElementGroupDisplayStatus) {
        sectionModel.updateVisibility(group, newStatus: newStatus)
    }
}

extension SelectSourceViewModel: M2PilotDelegate {
    func previewAvailabilityDidChange(pilot: M2Pilot) {
        if pilot.isPreviewAvailable {
            // we just enter the pro mode (preview available)

            // we hide all channel label

            var channels = Set<M2Channel>()
            channels = channels.union(pilot.previewManager.sources.map { $0.channel })
            channels = channels.union(pilot.programManager.sources.map { $0.channel })

            updateCellChannelLabels(channels: channels)
        }
    }
}

public extension Notification.Name {
    static let toggleElement = Notification.Name("ToggleElement")
}
