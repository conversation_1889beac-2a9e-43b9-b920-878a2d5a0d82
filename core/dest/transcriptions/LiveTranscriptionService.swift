//
//  LiveTranscriptionService.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 10.09.2024.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import Foundation
import Speech

private let logger = LsLogger(subsystem: "swi.ui", category: "LiveTranscriptionService")

@objc
class LiveTranscriptionService: NSObject {

    private var audioInjector: LiveTranscriptionAudioInjector

    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var currentTask: SFSpeechRecognitionTask?

    private let factory = VideoTranscriptionCaptionFactory()
    private let transcriptRepository: TranscriptRepository
    private var captions: [Caption] = []
    private let broadcastId: String?

    private var hasError: Bool = false

    @objc
    public init(gid: String, broadcastId: String?) {
        self.broadcastId = broadcastId
        let audioInjector = LiveTranscriptionAudioInjector()
        self.audioInjector = audioInjector
        self.transcriptRepository = TranscriptRepository(gid: gid, useDocumentDirectory: true)
        super.init()
        self.audioInjector.push = { [weak self] buffer in
            self?.appendAudioBuffer(buffer)
        }
        self.audioInjector.end = { [weak self] in
            self?.stopLiveTranscription()
        }
    }

    @objc
    public func start() -> UnsafeMutablePointer<MFLOW_PCM_PUSHABLE>? {
        guard PermissionsManager.shared.isGranted(.speech) else {
            logger.info("Speech permission not granted")
            return nil
        }

        // Create and configure the speech recognition request.
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            logger.error("Unable to created a SFSpeechAudioBufferRecognitionRequest object")
            return nil
        }
        recognitionRequest.requiresOnDeviceRecognition = true
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.addsPunctuation = true

        // create a new recognizer
        let language = Locale.current
        guard let recognizer = SFSpeechRecognizer(locale: language) else {
            logger.warning("Can't create recognizer with language: \(language)")
            return nil
        }

        guard recognizer.isAvailable, recognizer.supportsOnDeviceRecognition else {
            logger.warning("Recognizer not available: isAvailable: \(recognizer.isAvailable)," +
                           " supportsOnDeviceRecognition: \(recognizer.supportsOnDeviceRecognition)")
            return nil
        }

        captions = []
        currentTask = recognizer.recognitionTask(with: recognitionRequest, resultHandler: resultHandler)

        logger.info("Live transcription service ready (\(language))")
        return audioInjector.createInput()
    }

    public func appendAudioBuffer(_ sampleBuffer: CMSampleBuffer) {
        if !hasError {
            recognitionRequest?.appendAudioSampleBuffer(sampleBuffer)
        }
    }

    public func stopLiveTranscription() {
        if !hasError {
            logger.info("stopLiveTranscription")
            recognitionRequest?.endAudio()
        }
    }

    private func resultHandler(result: SFSpeechRecognitionResult?, error: (any Error)?) {
        if let error = error {
            hasError = true
            logger.warning("Error: \(error.localizedDescription)")

            // we can have an error but save previous captions
            if captions.count > 0 {
                self.manageSuccess()
            }
        } else if let result = result {
            
            //result.bestTranscription.formattedString

            let newCaptions = factory.createCaptions(from: result.bestTranscription.segments)
            captions.append(contentsOf: newCaptions)
            if result.isFinal {
                self.manageSuccess()
            }
        }
    }

    private func manageSuccess() {
        if !captions.isEmpty {
            if transcriptRepository.storeSubtitles(captions: captions) {
                logger.info("Transcription stored successfully")
            } else {
                logger.warning("Transcription storing failed")
            }

            guard let id = broadcastId else {
                return
            }

            storeCloudCaptions(broadcastId: id, captions: captions)
        } else {
            logger.info("No captions to save")
        }
    }

    /// Attempt to store cloud captions
    ///
    /// - Parameters:
    ///   - captions: array of captions to upload
    /// - Returns: if true, captions stored successfully
    private func storeCloudCaptions(broadcastId: String, captions: [Caption]) {
        Task { @Sendable in
            do {
                let success = try await BroadcastsAPI.shared.uploadCaptions(id: broadcastId,
                                                                            language: nil,
                                                                            captions: captions).success
                if success ?? false {
                    logger.info("Transcription uploaded successfully")
                } else {
                    logger.warning("Transcription uploading failed")
                }
            } catch {
                logger.warning("Transcription uploading failed")
            }
        }
    }
}
